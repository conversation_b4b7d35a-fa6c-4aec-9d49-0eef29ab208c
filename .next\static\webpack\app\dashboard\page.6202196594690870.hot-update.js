"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/page",{

/***/ "(app-pages-browser)/./app/dashboard/page.tsx":
/*!********************************!*\
  !*** ./app/dashboard/page.tsx ***!
  \********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ DashboardPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-auth/react */ \"(app-pages-browser)/./node_modules/next-auth/react/index.js\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_auth_react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react_i18next__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react-i18next */ \"(app-pages-browser)/./node_modules/react-i18next/dist/es/index.js\");\n/* harmony import */ var _components_layout_dashboard_layout__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/layout/dashboard-layout */ \"(app-pages-browser)/./components/layout/dashboard-layout.tsx\");\n/* harmony import */ var _components_dashboard_enhanced_stats__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/dashboard/enhanced-stats */ \"(app-pages-browser)/./components/dashboard/enhanced-stats.tsx\");\n/* harmony import */ var _components_dashboard_quick_actions__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/dashboard/quick-actions */ \"(app-pages-browser)/./components/dashboard/quick-actions.tsx\");\n/* harmony import */ var _components_dashboard_recent_activity__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/dashboard/recent-activity */ \"(app-pages-browser)/./components/dashboard/recent-activity.tsx\");\n/* harmony import */ var _components_dashboard_welcome_banner__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/dashboard/welcome-banner */ \"(app-pages-browser)/./components/dashboard/welcome-banner.tsx\");\n/* harmony import */ var _components_debug_i18n_test__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/debug/i18n-test */ \"(app-pages-browser)/./components/debug/i18n-test.tsx\");\n/* harmony import */ var _barrel_optimize_names_Clock_DollarSign_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,DollarSign,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_Clock_DollarSign_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,DollarSign,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/dollar-sign.js\");\n/* harmony import */ var _barrel_optimize_names_Clock_DollarSign_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,DollarSign,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_Clock_DollarSign_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,DollarSign,TrendingUp,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n// Mock dashboard data - replace with real data from your mock-data.ts\nconst mockDashboardData = {\n    totalBeneficiaries: 1247,\n    totalZakatDistributed: 2450000,\n    pendingApplications: 23,\n    activeDistributions: 8,\n    changes: {\n        beneficiaries: {\n            value: 12,\n            type: \"increase\",\n            period: \"last_month\"\n        },\n        zakat: {\n            value: 8,\n            type: \"increase\",\n            period: \"last_month\"\n        },\n        pending: {\n            value: 5,\n            type: \"decrease\",\n            period: \"last_week\"\n        },\n        distributions: {\n            value: 2,\n            type: \"increase\",\n            period: \"this_week\"\n        }\n    }\n};\nfunction DashboardPage() {\n    _s();\n    const { data: session } = (0,next_auth_react__WEBPACK_IMPORTED_MODULE_1__.useSession)() || {};\n    const { t } = (0,react_i18next__WEBPACK_IMPORTED_MODULE_2__.useTranslation)();\n    if (!session?.user) {\n        return null;\n    }\n    const userRole = session.user.role;\n    const data = mockDashboardData;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_dashboard_layout__WEBPACK_IMPORTED_MODULE_3__.DashboardLayout, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-8\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_welcome_banner__WEBPACK_IMPORTED_MODULE_7__.WelcomeBanner, {\n                    userName: session.user.name || \"\",\n                    userRole: userRole,\n                    className: \"mb-2\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\app\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 48,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_debug_i18n_test__WEBPACK_IMPORTED_MODULE_8__.I18nTest, {}, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\app\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 55,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_enhanced_stats__WEBPACK_IMPORTED_MODULE_4__.StatsGrid, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_enhanced_stats__WEBPACK_IMPORTED_MODULE_4__.EnhancedStats, {\n                            title: t(\"total_beneficiaries\"),\n                            value: data.totalBeneficiaries.toLocaleString(),\n                            change: data.changes.beneficiaries,\n                            icon: _barrel_optimize_names_Clock_DollarSign_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n                            color: \"success\",\n                            trend: [\n                                45,\n                                52,\n                                48,\n                                61,\n                                58,\n                                67,\n                                72\n                            ]\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 59,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_enhanced_stats__WEBPACK_IMPORTED_MODULE_4__.EnhancedStats, {\n                            title: t(\"total_zakat_distributed\"),\n                            value: `${(data.totalZakatDistributed / 1000000).toFixed(1)}M ${t(\"sar\")}`,\n                            change: data.changes.zakat,\n                            icon: _barrel_optimize_names_Clock_DollarSign_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"],\n                            color: \"primary\",\n                            trend: [\n                                30,\n                                35,\n                                42,\n                                38,\n                                45,\n                                50,\n                                48\n                            ]\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 67,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_enhanced_stats__WEBPACK_IMPORTED_MODULE_4__.EnhancedStats, {\n                            title: t(\"pending_applications\"),\n                            value: data.pendingApplications,\n                            change: data.changes.pending,\n                            icon: _barrel_optimize_names_Clock_DollarSign_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"],\n                            color: \"warning\",\n                            trend: [\n                                25,\n                                28,\n                                22,\n                                30,\n                                26,\n                                23,\n                                20\n                            ]\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 75,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_enhanced_stats__WEBPACK_IMPORTED_MODULE_4__.EnhancedStats, {\n                            title: t(\"active_distributions\"),\n                            value: data.activeDistributions,\n                            change: data.changes.distributions,\n                            icon: _barrel_optimize_names_Clock_DollarSign_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"],\n                            color: \"info\",\n                            trend: [\n                                15,\n                                18,\n                                22,\n                                19,\n                                25,\n                                28,\n                                30\n                            ]\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 83,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\app\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 58,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid gap-6 lg:grid-cols-3\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"lg:col-span-2\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_quick_actions__WEBPACK_IMPORTED_MODULE_5__.QuickActions, {}, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 97,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 96,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"lg:col-span-1\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_recent_activity__WEBPACK_IMPORTED_MODULE_6__.RecentActivity, {}, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 102,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 101,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\app\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 94,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid gap-6 md:grid-cols-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"h-64 rounded-xl border-2 border-dashed border-primary/20 bg-gradient-subtle flex items-center justify-center card-enhanced\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center space-y-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"h-16 w-16 mx-auto rounded-full bg-primary/10 flex items-center justify-center\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_DollarSign_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                            className: \"h-8 w-8 text-primary\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 111,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 110,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-lg font-semibold text-foreground\",\n                                                children: t(\"monthly_distributions\")\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 114,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-muted-foreground\",\n                                                children: \"Chart coming soon\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 115,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 113,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 109,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 108,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"h-64 rounded-xl border-2 border-dashed border-success/20 bg-gradient-subtle flex items-center justify-center card-enhanced\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center space-y-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"h-16 w-16 mx-auto rounded-full bg-success/10 flex items-center justify-center\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_DollarSign_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                            className: \"h-8 w-8 text-success\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 122,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 121,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-lg font-semibold text-foreground\",\n                                                children: t(\"beneficiary_categories\")\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 125,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-muted-foreground\",\n                                                children: \"Chart coming soon\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 126,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 124,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 120,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 119,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\app\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 107,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\app\\\\dashboard\\\\page.tsx\",\n            lineNumber: 46,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\app\\\\dashboard\\\\page.tsx\",\n        lineNumber: 45,\n        columnNumber: 5\n    }, this);\n}\n_s(DashboardPage, \"2lnELbWSk7RtWQt0gu+9/bzTjzU=\", false, function() {\n    return [\n        next_auth_react__WEBPACK_IMPORTED_MODULE_1__.useSession,\n        react_i18next__WEBPACK_IMPORTED_MODULE_2__.useTranslation\n    ];\n});\n_c = DashboardPage;\nvar _c;\n$RefreshReg$(_c, \"DashboardPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/dashboard/page.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./components/debug/i18n-test.tsx":
/*!****************************************!*\
  !*** ./components/debug/i18n-test.tsx ***!
  \****************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   I18nTest: function() { return /* binding */ I18nTest; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_i18next__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react-i18next */ \"(app-pages-browser)/./node_modules/react-i18next/dist/es/index.js\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* __next_internal_client_entry_do_not_use__ I18nTest auto */ \nvar _s = $RefreshSig$();\n\n\nfunction I18nTest() {\n    _s();\n    const { t, i18n } = (0,react_i18next__WEBPACK_IMPORTED_MODULE_1__.useTranslation)();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n        className: \"m-4\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                    children: \"i18n Debug Test\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\debug\\\\i18n-test.tsx\",\n                    lineNumber: 12,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\debug\\\\i18n-test.tsx\",\n                lineNumber: 11,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                className: \"space-y-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                children: \"Current Language:\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\debug\\\\i18n-test.tsx\",\n                                lineNumber: 16,\n                                columnNumber: 11\n                            }, this),\n                            \" \",\n                            i18n.language\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\debug\\\\i18n-test.tsx\",\n                        lineNumber: 15,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                children: \"Is i18n Ready:\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\debug\\\\i18n-test.tsx\",\n                                lineNumber: 19,\n                                columnNumber: 11\n                            }, this),\n                            \" \",\n                            i18n.isInitialized ? \"Yes\" : \"No\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\debug\\\\i18n-test.tsx\",\n                        lineNumber: 18,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                children: \"Welcome Translation:\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\debug\\\\i18n-test.tsx\",\n                                lineNumber: 22,\n                                columnNumber: 11\n                            }, this),\n                            ' \"',\n                            t(\"welcome\"),\n                            '\"'\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\debug\\\\i18n-test.tsx\",\n                        lineNumber: 21,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                children: \"Dashboard Translation:\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\debug\\\\i18n-test.tsx\",\n                                lineNumber: 25,\n                                columnNumber: 11\n                            }, this),\n                            ' \"',\n                            t(\"dashboard\"),\n                            '\"'\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\debug\\\\i18n-test.tsx\",\n                        lineNumber: 24,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                children: \"Login Translation:\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\debug\\\\i18n-test.tsx\",\n                                lineNumber: 28,\n                                columnNumber: 11\n                            }, this),\n                            ' \"',\n                            t(\"login\"),\n                            '\"'\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\debug\\\\i18n-test.tsx\",\n                        lineNumber: 27,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                children: \"Available Languages:\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\debug\\\\i18n-test.tsx\",\n                                lineNumber: 31,\n                                columnNumber: 11\n                            }, this),\n                            \" \",\n                            Object.keys(i18n.options.resources || {}).join(\", \")\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\debug\\\\i18n-test.tsx\",\n                        lineNumber: 30,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                children: \"Fallback Language:\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\debug\\\\i18n-test.tsx\",\n                                lineNumber: 34,\n                                columnNumber: 11\n                            }, this),\n                            \" \",\n                            i18n.options.fallbackLng\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\debug\\\\i18n-test.tsx\",\n                        lineNumber: 33,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                children: \"Raw Translation Test:\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\debug\\\\i18n-test.tsx\",\n                                lineNumber: 37,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"pre\", {\n                                className: \"bg-gray-100 p-2 rounded text-sm\",\n                                children: JSON.stringify({\n                                    welcome_ar: t(\"welcome\"),\n                                    welcome_en: i18n.getResource(\"en\", \"translation\", \"welcome\"),\n                                    current_lang: i18n.language\n                                }, null, 2)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\debug\\\\i18n-test.tsx\",\n                                lineNumber: 38,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\debug\\\\i18n-test.tsx\",\n                        lineNumber: 36,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\debug\\\\i18n-test.tsx\",\n                lineNumber: 14,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\debug\\\\i18n-test.tsx\",\n        lineNumber: 10,\n        columnNumber: 5\n    }, this);\n}\n_s(I18nTest, \"OZwazanA59tbNDUkc8lMSmTHj9Q=\", false, function() {\n    return [\n        react_i18next__WEBPACK_IMPORTED_MODULE_1__.useTranslation\n    ];\n});\n_c = I18nTest;\nvar _c;\n$RefreshReg$(_c, \"I18nTest\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/debug/i18n-test.tsx\n"));

/***/ })

});